{"main": {"id": "ab494086f8391abe", "type": "split", "children": [{"id": "64673bbc2af9b82d", "type": "tabs", "children": [{"id": "c3db0b669aa28e69", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "halo/api配置.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "pinned": true, "icon": "lucide-file", "title": "api配置"}}, {"id": "552c73d1f5088ce8", "type": "leaf", "state": {"type": "markdown", "state": {"file": "halo/Cursor 1.0+<PERSON> Task Master+Gemini 2.5 Pro 0605开发效率提升10倍！从产品需求文档生成到子任务分解到自动单元测试到全自动开发复杂项目！.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "Cursor 1.0+<PERSON> Task Master+Gemini 2.5 Pro 0605开发效率提升10倍！从产品需求文档生成到子任务分解到自动单元测试到全自动开发复杂项目！"}}, {"id": "895e13f74e1cffe8", "type": "leaf", "state": {"type": "markdown", "state": {"file": "halo/Claude Dev.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "<PERSON>"}}, {"id": "33edc28866fb20b1", "type": "leaf", "state": {"type": "markdown", "state": {"file": "halo/Liblib AI上线Kontext，门槛大幅降低！藏师傅手把手教你用它解决图片问题.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "Liblib AI上线Kontext，门槛大幅降低！藏师傅手把手教你用它解决图片问题"}}], "currentTab": 2}], "direction": "vertical"}, "left": {"id": "d9206aac6eef66d7", "type": "split", "children": [{"id": "00948546592b18f8", "type": "tabs", "children": [{"id": "f4110834997db068", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "d3cf7711b1fd2691", "type": "leaf", "state": {"type": "search", "state": {"query": "<PERSON> 详细教程", "matchingCase": false, "explainSearch": false, "collapseAll": true, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "288c8f7239601f8f", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}], "currentTab": 1}], "direction": "horizontal", "width": 400.5}, "right": {"id": "219dacf15d7a7ab9", "type": "split", "children": [{"id": "6374b1fdc9a929a2", "type": "tabs", "children": [{"id": "5ca11c58674127f8", "type": "leaf", "state": {"type": "backlink", "state": {"file": "searxng的api.md", "collapseAll": true, "extraContext": false, "sortOrder": "alphabetical", "showSearch": true, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "searxng的api 的反向链接列表"}}, {"id": "8145984c8316b410", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "halo/flowith教程详解.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "flowith教程详解 的出链列表"}}, {"id": "f960581969c7d565", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "ab82717815cd475e", "type": "leaf", "state": {"type": "outline", "state": {"file": "halo/Claude Dev.md", "followCursor": true, "showSearch": true, "searchQuery": ""}, "icon": "lucide-list", "title": "<PERSON> 的大纲"}}, {"id": "7dfd9e776c20ce98", "type": "leaf", "state": {"type": "infio-chat-view", "state": {}, "icon": "wand-sparkles", "title": "Infio chat"}}, {"id": "9da5d55163cd57d2", "type": "leaf", "state": {"type": "smtcmp-chat-view", "state": {}, "icon": "wand-sparkles", "title": "Smart composer chat"}}, {"id": "f776c56155a8fd8f", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-archive", "title": "添加笔记属性"}}], "currentTab": 3}], "direction": "horizontal", "width": 516.5}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-importer:打开 Markdown 格式转换器": false, "workspaces:管理工作区布局": false, "halo:发布当前文档到 Halo": false, "quickadd:QuickAdd": false, "smart-composer:Open smart composer": false, "infio-copilot:打开 Infio Copilot": false}}, "active": "895e13f74e1cffe8", "lastOpenFiles": ["🚀颠覆传统编程！Claude Code+Zen MCP实现多AI协作开发！效率提升20倍！Claude+Gemini 2.5+O3打造专业编程开发团队自动调用最适合的AI进行编码，开发效率提升20倍！.md", "Interview-找工作前一定背熟 常见面试题20问.md", "翟生-简历-001.html", "翟生-简历-001", "翟生-简历-01.html", "theme-fix.css", "halo/vps-国内外VPS云服务器推荐与选择指南：独立开发建站必读.md", "翟生-2.pdf", "简历-2.html", "简历-1.html", "halo/sse-mcpServers-modelscope-魔搭社区.md", "翟生-简历-精简版.md", "halo/Liblib AI上线Kontext，门槛大幅降低！藏师傅手把手教你用它解决图片问题.md", "翟生-简历-美化升级版.html", "翟生-简历-优化版.html", "翟生-简历.html", "halo/hadoop是什么.md", "未命名 4.md", "未命名 1.md", "未命名 2.md", "未命名 3.md", "halo/Agentic coding-我对各种 AI Coding Agent 工具的看法.md", "halo/Cursor 1.0+<PERSON> Task Master+Gemini 2.5 Pro 0605开发效率提升10倍！从产品需求文档生成到子任务分解到自动单元测试到全自动开发复杂项目！.md", "halo/api配置.md", "halo/Trust-值得信任的销售都有这三个特征.md", "halo/business-学习潮汕人：Web3支付之道，不在结算之道，而在地下钱庄的流通之道.md", "halo/business-张一鸣 推荐哲学的审视与反思.md", "halo/vibe coding-2025 年的 AI 协助编程观察.md", "未命名/ 4.md", "metadata_blog_post.md", "广州泽矩科技股份有限公司销售区域总监职位面试全面分析报告.md", "halo/DeepSeek-徒手造一个能对话的 AI 简历，助你当场拿下 Offer.md", "halo/prompt.md", "halo/fofa搜索指南.md", "halo/AI大模型制作PPT工具列表.md", "halo/prompt-cursor rules，让你用AI编程起飞.md", "canvas/未命名.canvas", "canvas/测试.canvas", "_resources/n8n-workflow-diagram.svg", "8bce0c3bcb3126b390fcc691684deb46.png", "e8603fb07699d92c16504052a976c055.png", "docs/vscode_extension/assets/icon.png", "docs/liubai/assets/liubai-logo.png", "docs/Coco AI/assets/favicon.png", "docs/chatgpt_next_web/assets/icon.png", "halo/Pasted image 20241002105910.png", "640 _4_.jpg", "image.png", "未命名 1.canvas", "未命名 2.canvas", "personal/未命名.canvas"]}